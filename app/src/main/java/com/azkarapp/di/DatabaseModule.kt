package com.azkarapp.di

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.preferencesDataStore
import com.azkarapp.data.database.AzkarDatabase
import com.azkarapp.data.database.dao.AudioFileDao
import com.azkarapp.data.database.dao.AzkarDao
import com.azkarapp.utils.AudioMetadataExtractor
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "azkar_preferences")

@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {

    @Provides
    @Singleton
    fun provideAzkarDatabase(@ApplicationContext context: Context): AzkarDatabase {
        return AzkarDatabase.create(context)
    }

    @Provides
    fun provideAzkarDao(database: AzkarDatabase): AzkarDao {
        return database.azkarDao()
    }

    @Provides
    fun provideAudioFileDao(database: AzkarDatabase): AudioFileDao {
        return database.audioFileDao()
    }

    @Provides
    @Singleton
    fun provideDataStore(@ApplicationContext context: Context): DataStore<Preferences> {
        return context.dataStore
    }

    @Provides
    @Singleton
    fun provideAudioMetadataExtractor(): AudioMetadataExtractor {
        return AudioMetadataExtractor()
    }
}
